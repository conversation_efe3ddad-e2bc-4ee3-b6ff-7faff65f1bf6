# app/core/supabase_client.py

"""
Supabase 客户端配置模块

提供 Supabase 客户端的初始化和配置，包括认证客户端和数据库客户端。
支持不同权限级别的客户端实例。
"""

from supabase import create_client, Client
from gotrue import SyncGoTrueClient
from loguru import logger
from typing import Optional

from backend.app.config.env import supabase_settings


class SupabaseClient:
    """Supabase 客户端管理类"""
    
    def __init__(self):
        self._anon_client: Optional[Client] = None
        self._service_client: Optional[Client] = None
        
    def get_anon_client(self) -> Client:
        """
        获取匿名客户端（用于用户认证操作）
        
        Returns:
            Client: Supabase 匿名客户端
        """
        if self._anon_client is None:
            if not supabase_settings.supabase_url or not supabase_settings.supabase_anon_key:
                raise ValueError("Supabase URL and ANON KEY must be configured")
                
            self._anon_client = create_client(
                supabase_settings.supabase_url,
                supabase_settings.supabase_anon_key
            )
            logger.info("Supabase anonymous client initialized")
            
        return self._anon_client
    
    def get_service_client(self) -> Client:
        """
        获取服务角色客户端（用于管理员操作）
        
        Returns:
            Client: Supabase 服务角色客户端
        """
        if self._service_client is None:
            if not supabase_settings.supabase_url or not supabase_settings.supabase_service_role_key:
                raise ValueError("Supabase URL and SERVICE ROLE KEY must be configured")
                
            self._service_client = create_client(
                supabase_settings.supabase_url,
                supabase_settings.supabase_service_role_key
            )
            logger.info("Supabase service role client initialized")
            
        return self._service_client
    
    def get_client_with_auth(self, access_token: str) -> Client:
        """
        获取带有用户认证的客户端
        
        Args:
            access_token: 用户的访问令牌
            
        Returns:
            Client: 带有用户认证的 Supabase 客户端
        """
        client = create_client(
            supabase_settings.supabase_url,
            supabase_settings.supabase_anon_key
        )
        
        # 设置用户会话
        client.auth.set_session(access_token, refresh_token="")
        
        return client


# 全局 Supabase 客户端实例
supabase_client = SupabaseClient()


def get_supabase_anon_client() -> Client:
    """获取匿名 Supabase 客户端的便捷函数"""
    return supabase_client.get_anon_client()


def get_supabase_service_client() -> Client:
    """获取服务角色 Supabase 客户端的便捷函数"""
    return supabase_client.get_service_client()


def get_supabase_user_client(access_token: str) -> Client:
    """获取用户认证 Supabase 客户端的便捷函数"""
    return supabase_client.get_client_with_auth(access_token)
