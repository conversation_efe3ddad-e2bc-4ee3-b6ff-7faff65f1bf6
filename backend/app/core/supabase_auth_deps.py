# app/core/supabase_auth_deps.py

"""
Supabase 认证依赖模块

提供基于 Supabase Auth 的 FastAPI 依赖注入函数。
替换原有的 JWT 认证中间件。
"""

from fastapi import Depends, HTTPException, status, Request
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from typing import Optional, Dict, Any, List
from loguru import logger

from backend.app.services.supabase_auth_service import SupabaseAuthService
from backend.app.core.deps import AsyncSessionDep


# HTTP Bearer 认证方案
security = HTTPBearer()


class SupabaseUser:
    """Supabase 用户信息类"""
    
    def __init__(self, user_data: Dict[str, Any]):
        self.id: str = user_data.get("id", "")
        self.email: str = user_data.get("email", "")
        self.user_metadata: Dict[str, Any] = user_data.get("user_metadata", {})
        self.app_metadata: Dict[str, Any] = user_data.get("app_metadata", {})
        self.created_at: str = user_data.get("created_at", "")
        self.updated_at: str = user_data.get("updated_at", "")
        self.email_confirmed_at: Optional[str] = user_data.get("email_confirmed_at")
        self.phone: Optional[str] = user_data.get("phone")
        self.phone_confirmed_at: Optional[str] = user_data.get("phone_confirmed_at")
        self.last_sign_in_at: Optional[str] = user_data.get("last_sign_in_at")
        
        # 从用户元数据中提取角色和权限信息
        self.roles: List[str] = self.user_metadata.get("roles", [])
        self.permissions: List[str] = self.user_metadata.get("permissions", [])
        
        # 原始数据
        self._raw_data = user_data
    
    def has_role(self, role: str) -> bool:
        """检查用户是否具有指定角色"""
        return role in self.roles
    
    def has_permission(self, permission: str) -> bool:
        """检查用户是否具有指定权限"""
        return permission in self.permissions
    
    def has_any_role(self, roles: List[str]) -> bool:
        """检查用户是否具有任意一个指定角色"""
        return any(role in self.roles for role in roles)
    
    def has_any_permission(self, permissions: List[str]) -> bool:
        """检查用户是否具有任意一个指定权限"""
        return any(permission in self.permissions for permission in permissions)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return self._raw_data


async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security)
) -> SupabaseUser:
    """
    获取当前认证用户
    
    Args:
        credentials: HTTP Bearer 认证凭据
        
    Returns:
        SupabaseUser: 当前用户信息
        
    Raises:
        HTTPException: 认证失败时抛出异常
    """
    if not credentials or not credentials.credentials:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Missing authentication token",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    try:
        # 验证 JWT 令牌
        payload = SupabaseAuthService.verify_jwt_token(credentials.credentials)
        user_id = payload.get("sub")
        
        if not user_id:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid token payload",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        # 获取用户信息
        user_data = await SupabaseAuthService.get_user_by_id(user_id)
        
        if not user_data:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="User not found",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        return SupabaseUser(user_data)
        
    except HTTPException:
        # 重新抛出 HTTPException
        raise
    except Exception as e:
        logger.error(f"Authentication failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authentication failed",
            headers={"WWW-Authenticate": "Bearer"},
        )


async def get_optional_current_user(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security)
) -> Optional[SupabaseUser]:
    """
    获取可选的当前认证用户（允许匿名访问）
    
    Args:
        credentials: HTTP Bearer 认证凭据（可选）
        
    Returns:
        Optional[SupabaseUser]: 当前用户信息，未认证则返回None
    """
    if not credentials or not credentials.credentials:
        return None
    
    try:
        return await get_current_user(credentials)
    except HTTPException:
        return None


class RequireRoles:
    """
    角色权限检查依赖工厂
    
    用于检查用户是否具有指定的角色
    """
    
    def __init__(self, required_roles: List[str], require_all: bool = False):
        """
        初始化角色检查器
        
        Args:
            required_roles: 必需的角色列表
            require_all: 是否需要所有角色（True）还是任意一个角色（False）
        """
        self.required_roles = required_roles
        self.require_all = require_all
    
    def __call__(self, current_user: SupabaseUser = Depends(get_current_user)) -> SupabaseUser:
        """
        执行角色检查
        
        Args:
            current_user: 当前用户
            
        Returns:
            SupabaseUser: 验证通过的用户
            
        Raises:
            HTTPException: 权限不足时抛出403错误
        """
        if self.require_all:
            # 需要所有角色
            missing_roles = [role for role in self.required_roles if not current_user.has_role(role)]
            if missing_roles:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"Missing required roles: {', '.join(missing_roles)}"
                )
        else:
            # 需要任意一个角色
            if not current_user.has_any_role(self.required_roles):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"Missing any of required roles: {', '.join(self.required_roles)}"
                )
        
        return current_user


class RequirePermissions:
    """
    权限检查依赖工厂
    
    用于检查用户是否具有指定的权限
    """
    
    def __init__(self, required_permissions: List[str], require_all: bool = False):
        """
        初始化权限检查器
        
        Args:
            required_permissions: 必需的权限列表
            require_all: 是否需要所有权限（True）还是任意一个权限（False）
        """
        self.required_permissions = required_permissions
        self.require_all = require_all
    
    def __call__(self, current_user: SupabaseUser = Depends(get_current_user)) -> SupabaseUser:
        """
        执行权限检查
        
        Args:
            current_user: 当前用户
            
        Returns:
            SupabaseUser: 验证通过的用户
            
        Raises:
            HTTPException: 权限不足时抛出403错误
        """
        if self.require_all:
            # 需要所有权限
            missing_permissions = [perm for perm in self.required_permissions if not current_user.has_permission(perm)]
            if missing_permissions:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"Missing required permissions: {', '.join(missing_permissions)}"
                )
        else:
            # 需要任意一个权限
            if not current_user.has_any_permission(self.required_permissions):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"Missing any of required permissions: {', '.join(self.required_permissions)}"
                )
        
        return current_user


# 便捷的依赖函数
def require_admin() -> RequireRoles:
    """要求管理员角色"""
    return RequireRoles(["admin"])


def require_user() -> RequireRoles:
    """要求用户角色（包括管理员）"""
    return RequireRoles(["user", "admin"], require_all=False)
