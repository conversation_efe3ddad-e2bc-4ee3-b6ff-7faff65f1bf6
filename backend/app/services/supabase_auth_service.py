# app/services/supabase_auth_service.py

"""
Supabase 认证服务模块

使用 Supabase Auth 替换原有的 JWT 认证系统。
提供用户注册、登录、登出、密码重置等功能。
"""

import jwt
from typing import Optional, Dict, Any, List
from fastapi import HTTPException, status
from loguru import logger
from datetime import datetime, timezone

from backend.app.core.supabase_client import get_supabase_anon_client, get_supabase_service_client
from backend.app.config.env import supabase_settings
from backend.app.api.v1.schemas.user_schemas import UserCreate


class SupabaseAuthService:
    """Supabase 认证服务类"""
    
    @staticmethod
    async def sign_up(email: str, password: str, user_metadata: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        用户注册
        
        Args:
            email: 用户邮箱
            password: 用户密码
            user_metadata: 用户元数据（可选）
            
        Returns:
            Dict: 注册结果，包含用户信息和会话
            
        Raises:
            HTTPException: 注册失败时抛出异常
        """
        try:
            supabase = get_supabase_anon_client()
            
            # 准备注册数据
            sign_up_data = {
                "email": email,
                "password": password
            }
            
            if user_metadata:
                sign_up_data["data"] = user_metadata
            
            # 执行注册
            response = supabase.auth.sign_up(sign_up_data)
            
            if response.user is None:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="User registration failed"
                )
            
            logger.info(f"User registered successfully: {email}")
            
            return {
                "user": response.user,
                "session": response.session,
                "message": "Registration successful. Please check your email for verification." if not supabase_settings.supabase_auth_auto_confirm else "Registration successful."
            }
            
        except Exception as e:
            logger.error(f"Registration failed for {email}: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Registration failed: {str(e)}"
            )
    
    @staticmethod
    async def sign_in(email: str, password: str) -> Dict[str, Any]:
        """
        用户登录
        
        Args:
            email: 用户邮箱
            password: 用户密码
            
        Returns:
            Dict: 登录结果，包含用户信息和会话
            
        Raises:
            HTTPException: 登录失败时抛出异常
        """
        try:
            supabase = get_supabase_anon_client()
            
            # 执行登录
            response = supabase.auth.sign_in_with_password({
                "email": email,
                "password": password
            })
            
            if response.user is None or response.session is None:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Invalid email or password"
                )
            
            logger.info(f"User signed in successfully: {email}")
            
            return {
                "user": response.user,
                "session": response.session,
                "access_token": response.session.access_token,
                "refresh_token": response.session.refresh_token,
                "expires_at": response.session.expires_at
            }
            
        except Exception as e:
            logger.error(f"Sign in failed for {email}: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid email or password"
            )
    
    @staticmethod
    async def sign_out(access_token: str) -> Dict[str, Any]:
        """
        用户登出
        
        Args:
            access_token: 用户的访问令牌
            
        Returns:
            Dict: 登出结果
        """
        try:
            supabase = get_supabase_anon_client()
            
            # 设置用户会话
            supabase.auth.set_session(access_token, refresh_token="")
            
            # 执行登出
            supabase.auth.sign_out()
            
            logger.info("User signed out successfully")
            
            return {"message": "Signed out successfully"}
            
        except Exception as e:
            logger.error(f"Sign out failed: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Sign out failed: {str(e)}"
            )
    
    @staticmethod
    async def refresh_session(refresh_token: str) -> Dict[str, Any]:
        """
        刷新用户会话
        
        Args:
            refresh_token: 刷新令牌
            
        Returns:
            Dict: 新的会话信息
        """
        try:
            supabase = get_supabase_anon_client()
            
            # 刷新会话
            response = supabase.auth.refresh_session(refresh_token)
            
            if response.session is None:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Invalid refresh token"
                )
            
            logger.info("Session refreshed successfully")
            
            return {
                "session": response.session,
                "access_token": response.session.access_token,
                "refresh_token": response.session.refresh_token,
                "expires_at": response.session.expires_at
            }
            
        except Exception as e:
            logger.error(f"Session refresh failed: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Session refresh failed"
            )
    
    @staticmethod
    async def reset_password(email: str) -> Dict[str, Any]:
        """
        发送密码重置邮件
        
        Args:
            email: 用户邮箱
            
        Returns:
            Dict: 重置结果
        """
        try:
            supabase = get_supabase_anon_client()
            
            # 发送密码重置邮件
            supabase.auth.reset_password_email(
                email,
                {"redirect_to": supabase_settings.supabase_auth_redirect_url}
            )
            
            logger.info(f"Password reset email sent to: {email}")
            
            return {"message": "Password reset email sent successfully"}
            
        except Exception as e:
            logger.error(f"Password reset failed for {email}: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Password reset failed: {str(e)}"
            )
    
    @staticmethod
    def verify_jwt_token(token: str) -> Dict[str, Any]:
        """
        验证 JWT 令牌
        
        Args:
            token: JWT 令牌
            
        Returns:
            Dict: 解码后的令牌载荷
            
        Raises:
            HTTPException: 令牌无效时抛出异常
        """
        try:
            # 使用 Supabase JWT 密钥验证令牌
            payload = jwt.decode(
                token,
                supabase_settings.supabase_jwt_secret,
                algorithms=["HS256"],
                audience="authenticated"
            )
            
            # 检查令牌是否过期
            if payload.get("exp", 0) < datetime.now(timezone.utc).timestamp():
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Token has expired"
                )
            
            return payload
            
        except jwt.InvalidTokenError as e:
            logger.error(f"JWT validation failed: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid token"
            )
    
    @staticmethod
    async def get_user_by_id(user_id: str) -> Optional[Dict[str, Any]]:
        """
        通过用户ID获取用户信息
        
        Args:
            user_id: 用户ID
            
        Returns:
            Optional[Dict]: 用户信息，未找到则返回None
        """
        try:
            supabase = get_supabase_service_client()
            
            # 使用服务角色客户端获取用户信息
            response = supabase.auth.admin.get_user_by_id(user_id)
            
            return response.user if response.user else None
            
        except Exception as e:
            logger.error(f"Failed to get user {user_id}: {str(e)}")
            return None
    
    @staticmethod
    async def update_user_metadata(user_id: str, metadata: Dict[str, Any]) -> Dict[str, Any]:
        """
        更新用户元数据
        
        Args:
            user_id: 用户ID
            metadata: 要更新的元数据
            
        Returns:
            Dict: 更新结果
        """
        try:
            supabase = get_supabase_service_client()
            
            # 更新用户元数据
            response = supabase.auth.admin.update_user_by_id(
                user_id,
                {"user_metadata": metadata}
            )
            
            if response.user is None:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="User not found"
                )
            
            logger.info(f"User metadata updated for user: {user_id}")
            
            return {"user": response.user, "message": "User metadata updated successfully"}
            
        except Exception as e:
            logger.error(f"Failed to update user metadata for {user_id}: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Failed to update user metadata: {str(e)}"
            )
