"""
用户管理API
"""
from fastapi import APIRouter, Depends, HTTPException, status
from loguru import logger

from backend.app.services.auth_service import AuthService
from backend.app.core.deps import AsyncSessionDep
from backend.app.models import Permission, UserApiToken
from backend.app.api.v1.schemas.user_schemas import (
    UserRoleAssign, UserPermissionsResponse, UserRolesResponse, UserRoleRemove,
    PermissionResponse, UserUpdate, TokenScopeRequest
)
from backend.app.repo.user_repository import UserRepository, RoleRepository, PermissionRepository, UserTokenRepository

router = APIRouter(
    responses={401: {"description": "Unauthorized"}},
)


# 1. 为用户分配角色
@router.post("/assign_role_to_user")
async def assign_role_to_user(assignment: UserRoleAssign, db: AsyncSessionDep):
    """
    为用户分配角色

    需要 manage_users 权限
    """
    try:
        # 获取用户
        user_repo = UserRepository(db)

        user = await user_repo.get_one_by_username_with_roles(assignment.username)

        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"用户 '{assignment.username}' 不存在"
            )
        logger.info(f"获取用户: {user}")

        # 获取角色
        role_repo = RoleRepository(db)
        added_roles = []
        for role_name in assignment.role_names:

            role = await role_repo.get_one_by_name(role_name)

            if not role:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"角色 '{role_name}' 不存在"
                )
            logger.info(f"获取角色: {role}")

            # 检查用户是否已经拥有该角色
            if role not in user.roles:
                logger.info(f"为用户 {user.username} 分配角色: {role.name}")
                user.roles.append(role)
                added_roles.append(role)

        # 使用 try/except 包装 commit 操作
        try:
            await db.commit()
        except Exception as commit_error:
            logger.error(f"提交事务失败: {commit_error}")
            await db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"提交事务失败: {str(commit_error)}"
            )

        return {
            "message": f"已成功为用户 '{user.username}' 分配角色 '{', '.join(role.name.value for role in added_roles)} '"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"分配角色失败: {e}")
        raise HTTPException(status_code=500, detail=f"分配角色失败: {str(e)}")


#  查看用户的角色
@router.get("/user_role/{username}", response_model=UserRolesResponse)
async def get_user_roles(username: str, db: AsyncSessionDep, ):
    """获取用户的角色"""
    try:
        # 获取用户及其角色
        user_repo = UserRepository(db)
        user = await user_repo.get_one_by_username_with_roles(username)

        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"用户 '{username}' 不存在"
            )

        return UserRolesResponse(username=user.username, role_names=[role.name.value for role in user.roles])

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取用户角色失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取用户角色失败: {str(e)}")


# 获取当前用户权限
@router.get("/get_current_user_permissions", response_model=UserPermissionsResponse)
async def get_current_user_permissions(*,permissions: list[Permission] = Depends(AuthService.get_user_permission),
                                       username: str = Depends(AuthService.get_current_user)):
    """获取当前用户的所有权限"""
    # 将 Permission 对象转换为 PermissionResponse
    permission_responses = [
        PermissionResponse(
            id=perm.id,
            name=perm.name,
            description=perm.description
        ) for perm in permissions
    ]
    return UserPermissionsResponse(username=username, permissions=permission_responses)


# 从用户中删除角色
@router.post("/remove_roles_from_user")
async def remove_roles_from_user(assignment: UserRoleRemove, db: AsyncSessionDep):
    """
    从用户中移除角色

    需要 manage_users 权限
    """
    try:
        # 获取用户及其角色
        user_repo = UserRepository(db)
        user = await user_repo.get_one_by_username_with_roles(assignment.username)

        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"用户 '{assignment.username}' 不存在"
            )
        logger.info(f"获取用户: {user}")

        # 获取要移除的角色
        role_repo = RoleRepository(db)
        removed_roles = []
        for role_name in assignment.role_names:
            role = await role_repo.get_one_by_name(role_name)

            if not role:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"角色 '{role_name}' 不存在"
                )
            logger.info(f"获取角色: {role.name.value}")

            # 检查用户是否已经拥有该角色
            for role in user.roles:
                logger.info(f"从用户 {user.username} 移除角色: {role.name}")
                user.roles.remove(role)
                removed_roles.append(role)
            else:
                logger.info(f"用户 {user.username} 没有角色: {role.name}")

        # 提交更改
        await db.commit()

        if not removed_roles:
            return {"message": f"用户 '{user.username}' 没有需要移除的角色"}

        # 构建角色名称列表
        removed_role_list = [role.name.value for role in removed_roles]

        return {
            "message": f"已成功从用户 '{user.username}' 移除角色 '{', '.join(removed_role_list)}'"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"移除角色失败: {e}")
        raise HTTPException(status_code=500, detail=f"移除角色失败: {str(e)}")


# 修改用户密码
@router.post("/update_password")
async def update_password(password_data: UserUpdate, db: AsyncSessionDep,
                          current_user: str = Depends(AuthService.get_current_user)):
    """
    修改用户密码

    用户需要提供当前用户名和密码进行验证，然后可以更新密码
    """
    try:

        # 检查是否是当前用户
        if password_data.username != current_user:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="您没有权限修改此用户的密码",
                headers={"WWW-Authenticate": "Bearer"},
            )

        # 验证当前用户名和密码
        user = await AuthService.verify_user(password_data.username, password_data.current_password, db)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户名或密码不正确",
                headers={"WWW-Authenticate": "Bearer"},
            )

        # 检查新密码是否与当前密码相同
        if AuthService.verify_password(password_data.new_password, user.password):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="新密码不能与当前密码相同",
                headers={"WWW-Authenticate": "Bearer"},
            )

        # 加密新密码
        hashed_password = AuthService.get_password_hash(password_data.new_password)

        # 更新用户密码
        user_repo = UserRepository(db)
        updated_user = await user_repo.update(user.id, {"password": hashed_password})

        # 添加提交事务
        await db.commit()

        if not updated_user:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="更新密码失败",
            )

        return {"message": "密码更新成功"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新密码失败: {e}")
        raise HTTPException(status_code=500, detail=f"更新密码失败: {str(e)}")


@router.post("/create_api_token")
async def create_api_token(
    request_data: TokenScopeRequest,  # 使用请求模型
    db: AsyncSessionDep,
    current_user: str = Depends(AuthService.get_current_user)
):
    """
    创建带有特定权限范围的令牌

    用户可以创建包含自己已有权限的令牌，或者使用预定义的权限模板
    """
    try:
        user_repo = UserRepository(db)
        permission_repo = PermissionRepository(db)

        # 获取当前用户
        user = await user_repo.get_one_by_username(current_user)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"用户{current_user}不存在"
            )

        # 处理权限范围
        scopes = []

        # 如果指定了预定义模板
        if request_data.template:
            if request_data.template == "init_roles_permissions":
                scopes = ["manage_users", "create_permission"]
            elif request_data.template == "download_files":
                scopes = ["download_file", "read_database"]
            # 可以添加更多预定义模板...
            else:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"未知的权限模板: {request_data.template}"
                )
        # 如果直接指定了权限范围
        elif request_data.scopes:
            scopes = request_data.scopes

        # 验证权限
        if scopes:
            # 获取所有系统权限
            permissions = await permission_repo.get_all()
            valid_permission_names = [perm.name.value for perm in permissions]

            # 检查请求的权限是否有效
            invalid_scopes = [scope for scope in scopes if scope not in valid_permission_names]
            if invalid_scopes:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"请求的权限范围无效: {', '.join(invalid_scopes)}"
                )

            # 检查用户是否拥有请求的权限
            user_permissions = await AuthService.get_user_permission(username=current_user, db=db)
            user_permission_names = [perm.name for perm in user_permissions]

            unauthorized_scopes = [scope for scope in scopes if scope not in user_permission_names]
            if unauthorized_scopes:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"您没有权限创建包含以下权限的令牌: {', '.join(unauthorized_scopes)}"
                )

        # 创建令牌
        token = create_api_token({"sub": user.username}, scopes=scopes)

        # 保存令牌
        user_token = UserApiToken(user_id=user.id, token=token, permissions=scopes)
        user_token_repo = UserTokenRepository(db)
        await user_token_repo.create_entity(user_token)
        await db.commit()

        return {
            "access_token": token,
            "token_type": "bearer",
            "scopes": scopes
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"创建权限令牌失败: {str(e)}")
