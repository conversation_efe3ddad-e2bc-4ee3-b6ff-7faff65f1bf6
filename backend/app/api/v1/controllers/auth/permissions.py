"""
权限管理API
"""
from fastapi import APIRouter, Depends, HTTPException
from loguru import logger

from backend.app.core.deps import AsyncSessionDep

from backend.app.api.v1.schemas.user_schemas import PermissionCreate, AuthContext
from backend.app.repo.user_repository import PermissionRepository
from backend.app.services.init_data_service import init_roles, init_permissions,init_admin_role_permission
from backend.app.core.auth_deps import RequireApiScopes

router = APIRouter(
    responses={401: {"description": "Unauthorized"}},
)


# 0. 创建权限
@router.post("/create_permission")
async def create_permission(permission_data: PermissionCreate, db: AsyncSessionDep, auth: AuthContext = Depends(RequireApiScopes())):
    try:
        # 使用仓储类创建权限
        permission_repo = PermissionRepository(db)
        permission = await permission_repo.create(permission_data.model_dump())

        # 添加提交事务
        await db.commit()

        if permission:
            logger.info(f"权限 {permission_data.name} 创建成功")
            return {"message": "Permission created successfully"}
        else:
            raise HTTPException(status_code=500, detail="创建权限失败")
    except Exception as e:
        logger.error(f"创建权限失败: {e}")
        raise HTTPException(status_code=500, detail=f"创建权限失败: {str(e)}")


@router.post("/init_roles_permissions")
async def init_roles_permissions(db: AsyncSessionDep):
    logger.debug("开始初始化基本角色、权限...")

    try:

        await init_roles(db)
        await init_permissions(db)

        logger.success("基本角色、权限初始化成功")
        return {"message": "基本角色、权限初始化成功"}

    except Exception as e:
        logger.error(f"初始化角色、权限失败: {e}")
        raise HTTPException(status_code=500, detail=f"初始化角色、权限失败: {str(e)}")

@router.post("/init_admin_permissions")
async def init_admin_permissions(db: AsyncSessionDep):
    logger.debug("开始初始化admin权限...")

    try:

        result = await init_admin_role_permission(db)
        if result.get("success"):
            logger.success("admin权限初始化成功")
            return {"success":True ,"message": "admin权限初始化成功"}
        else:
            logger.error(f"admin权限初始化失败: {result.get('message')}")
            raise HTTPException(status_code=500, detail=f"admin权限初始化失败: {result.get('message')}")



    except Exception as e:
        logger.error(f"admin权限初始化失败: {e}")