"""
角色管理API
"""
from fastapi import APIRouter, HTTPException, status
from loguru import logger


from backend.app.core.deps import AsyncSessionDep
from backend.app.api.v1.schemas.user_schemas import (
    RolePermissionAssign, RolePermissionsResponse, RolePermissionRemove,
    PermissionResponse
)
from backend.app.repo.user_repository import RoleRepository, PermissionRepository

router = APIRouter(
    responses={401: {"description": "Unauthorized"}},
)


# 为角色分配权限
@router.post("/assign_permissions_to_role")
async def assign_permissions_to_role(assignment: RolePermissionAssign, db: AsyncSessionDep):
    """
    为角色分配权限 - 追加模式
    """
    try:
        # 获取角色
        role_repo = RoleRepository(db)
        role = await role_repo.get_one_by_name_with_permissions(assignment.role_name)

        if not role:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"角色 '{assignment.role_name}' 不存在"
            )

        # 获取所有指定的权限
        permission_repo = PermissionRepository(db)
        added_permissions = []
        for perm_id in assignment.permission_ids:
            permission = await permission_repo.get_by_id(perm_id)
            if not permission:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"权限ID {perm_id} 不存在"
                )
            logger.info(f"获取权限: {permission.name}")

            # 检查权限是否已经存在于角色的权限列表中
            if permission not in role.permissions:
                logger.info(f"为角色 {role.name} 分配权限: {permission.name}")
                role.permissions.append(permission)
                added_permissions.append(permission)
            else:
                logger.info(f"角色 {role.name} 已经拥有权限: {permission.name}")

        # 使用 try/except 包装 commit 操作
        try:
            await db.commit()
        except Exception as commit_error:
            logger.error(f"提交事务失败: {commit_error}")
            await db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"提交事务失败: {str(commit_error)}"
            )

        return {
            "message": f"已成功为角色 '{role.name}' 分配权限 '{', '.join(permission.name for permission in added_permissions)} '"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"分配权限失败: {e}")
        raise HTTPException(status_code=500, detail=f"分配权限失败: {str(e)}")


# 查看角色的权限
@router.get("/role_permissions/{role_name}")
async def get_role_permissions(role_name: str, db: AsyncSessionDep):
    """获取角色的所有权限"""
    try:
        # 获取角色及其权限
        role_repo = RoleRepository(db)
        role = await role_repo.get_one_by_name_with_permissions(role_name)

        if not role:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"角色 '{role_name}' 不存在"
            )

        permission_responses = [
            PermissionResponse(
                id=perm.id,
                name=perm.name,
                description=perm.description
            ) for perm in role.permissions
        ]
        return RolePermissionsResponse(role_name=role.name, permissions=permission_responses)


    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取角色权限失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取角色权限失败: {str(e)}")


# 从角色中移除权限
@router.post("/remove_permissions_from_role", )
async def remove_permissions_from_role(assignment: RolePermissionRemove, db: AsyncSessionDep):
    """
    从角色中移除权限

    需要 manage_users 权限
    """
    try:
        # 获取角色及其权限
        role_repo = RoleRepository(db)
        role = await role_repo.get_one_by_name_with_permissions(assignment.role_name)

        if not role:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"角色 '{assignment.role_name}' 不存在"
            )

        # 获取所有指定的权限
        permission_repo = PermissionRepository(db)
        removed_permissions = []
        for perm_id in assignment.permission_ids:
            permission = await permission_repo.get_by_id(perm_id)
            if not permission:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"权限ID {perm_id} 不存在"
                )
            logger.info(f"获取权限: {permission.name}")

            # 检查权限是否存在于角色的权限列表中
            if permission in role.permissions:
                logger.info(f"从角色 {role.name} 移除权限: {permission.name}")
                role.permissions.remove(permission)
                removed_permissions.append(permission)
            else:
                logger.info(f"角色 {role.name} 没有权限: {permission.name}")

        # 提交更改
        await db.commit()

        if not removed_permissions:
            return {"message": f"角色 '{role.name}' 没有需要移除的权限"}

        # 构建权限名称列表
        removed_perm = [f"{perm.id}:{perm.name}" for perm in removed_permissions]

        return {
            "message": f"已成功从角色 '{role.name}' 移除权限 '{', '.join(removed_perm)}'"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"移除权限失败: {e}")
        raise HTTPException(status_code=500, detail=f"移除权限失败: {str(e)}")
