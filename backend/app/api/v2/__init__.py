# app/api/v2/__init__.py

"""
API v2 模块

基于 Supabase Auth 的新一代认证系统 API。
提供更现代化的认证体验和更好的安全性。
"""

from fastapi import APIRouter

from .controllers.auth import auth_router as v2_auth_router
from .controllers.douyin import douyin_router as v2_douyin_router

# 创建 v2 API 主路由
v2_router = APIRouter(prefix="/v2")

# 注册子模块路由
v2_router.include_router(
    v2_auth_router,
    prefix="/auth",
    tags=["V2 Authentication"]
)

v2_router.include_router(
    v2_douyin_router,
    prefix="/douyin",
    tags=["V2 Douyin"]
)

__all__ = ["v2_router"]
