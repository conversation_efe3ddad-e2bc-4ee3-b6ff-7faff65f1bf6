# app/api/v2/controllers/auth/users.py

"""
V2 用户管理 API

基于 Supabase Auth 的用户管理功能。
提供用户信息更新、角色管理等功能。
"""

from fastapi import APIRouter, HTTPException, status, Depends
from pydantic import BaseModel, Field
from typing import Optional, Dict, Any, List
from loguru import logger

from backend.app.services.supabase_auth_service import SupabaseAuthService
from backend.app.core.supabase_auth_deps import (
    get_current_user, 
    SupabaseUser, 
    require_admin,
    RequireRoles,
    RequirePermissions
)


router = APIRouter(
    responses={401: {"description": "Unauthorized"}},
)


# Pydantic 模型
class UpdateUserMetadataRequest(BaseModel):
    """更新用户元数据请求模型"""
    username: Optional[str] = Field(None, description="用户名")
    full_name: Optional[str] = Field(None, description="全名")
    avatar_url: Optional[str] = Field(None, description="头像URL")
    bio: Optional[str] = Field(None, description="个人简介")


class UpdateUserRolesRequest(BaseModel):
    """更新用户角色请求模型"""
    user_id: str = Field(..., description="用户ID")
    roles: List[str] = Field(..., description="角色列表")


class UpdateUserPermissionsRequest(BaseModel):
    """更新用户权限请求模型"""
    user_id: str = Field(..., description="用户ID")
    permissions: List[str] = Field(..., description="权限列表")


class UserResponse(BaseModel):
    """用户响应模型"""
    id: str = Field(..., description="用户ID")
    email: str = Field(..., description="用户邮箱")
    user_metadata: Dict[str, Any] = Field(..., description="用户元数据")
    created_at: str = Field(..., description="创建时间")
    updated_at: str = Field(..., description="更新时间")
    last_sign_in_at: Optional[str] = Field(None, description="最后登录时间")


class MessageResponse(BaseModel):
    """消息响应模型"""
    message: str = Field(..., description="响应消息")


@router.get("/profile", response_model=UserResponse)
async def get_user_profile(current_user: SupabaseUser = Depends(get_current_user)):
    """
    获取当前用户详细信息
    
    Args:
        current_user: 当前用户
        
    Returns:
        UserResponse: 用户详细信息
    """
    return UserResponse(
        id=current_user.id,
        email=current_user.email,
        user_metadata=current_user.user_metadata,
        created_at=current_user.created_at,
        updated_at=current_user.updated_at,
        last_sign_in_at=current_user.last_sign_in_at
    )


@router.put("/profile", response_model=MessageResponse)
async def update_user_profile(
    request: UpdateUserMetadataRequest,
    current_user: SupabaseUser = Depends(get_current_user)
):
    """
    更新当前用户的个人信息
    
    Args:
        request: 更新请求数据
        current_user: 当前用户
        
    Returns:
        MessageResponse: 更新成功响应
    """
    try:
        # 准备更新的元数据
        updated_metadata = current_user.user_metadata.copy()
        
        if request.username is not None:
            updated_metadata["username"] = request.username
        if request.full_name is not None:
            updated_metadata["full_name"] = request.full_name
        if request.avatar_url is not None:
            updated_metadata["avatar_url"] = request.avatar_url
        if request.bio is not None:
            updated_metadata["bio"] = request.bio
        
        # 更新用户元数据
        result = await SupabaseAuthService.update_user_metadata(
            user_id=current_user.id,
            metadata=updated_metadata
        )
        
        return MessageResponse(message="Profile updated successfully")
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Profile update failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Profile update failed: {str(e)}"
        )


@router.get("/list")
async def list_users(
    current_user: SupabaseUser = Depends(require_admin()),
    page: int = 1,
    per_page: int = 20
):
    """
    获取用户列表（仅管理员）
    
    Args:
        current_user: 当前用户（必须是管理员）
        page: 页码
        per_page: 每页数量
        
    Returns:
        Dict: 用户列表和分页信息
    """
    try:
        from backend.app.core.supabase_client import get_supabase_service_client
        
        supabase = get_supabase_service_client()
        
        # 获取用户列表
        response = supabase.auth.admin.list_users(
            page=page,
            per_page=per_page
        )
        
        users = []
        if response.users:
            for user in response.users:
                users.append({
                    "id": user.id,
                    "email": user.email,
                    "user_metadata": user.user_metadata or {},
                    "created_at": user.created_at,
                    "updated_at": user.updated_at,
                    "last_sign_in_at": user.last_sign_in_at,
                    "email_confirmed_at": user.email_confirmed_at
                })
        
        return {
            "users": users,
            "total": len(users),
            "page": page,
            "per_page": per_page
        }
        
    except Exception as e:
        logger.error(f"List users failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list users: {str(e)}"
        )


@router.put("/roles", response_model=MessageResponse)
async def update_user_roles(
    request: UpdateUserRolesRequest,
    current_user: SupabaseUser = Depends(require_admin())
):
    """
    更新用户角色（仅管理员）
    
    Args:
        request: 更新角色请求数据
        current_user: 当前用户（必须是管理员）
        
    Returns:
        MessageResponse: 更新成功响应
    """
    try:
        # 获取目标用户信息
        target_user = await SupabaseAuthService.get_user_by_id(request.user_id)
        
        if not target_user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        # 更新用户角色
        updated_metadata = target_user.get("user_metadata", {}).copy()
        updated_metadata["roles"] = request.roles
        
        result = await SupabaseAuthService.update_user_metadata(
            user_id=request.user_id,
            metadata=updated_metadata
        )
        
        logger.info(f"User roles updated by admin {current_user.email}: {request.user_id} -> {request.roles}")
        
        return MessageResponse(message="User roles updated successfully")
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Update user roles failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to update user roles: {str(e)}"
        )


@router.put("/permissions", response_model=MessageResponse)
async def update_user_permissions(
    request: UpdateUserPermissionsRequest,
    current_user: SupabaseUser = Depends(require_admin())
):
    """
    更新用户权限（仅管理员）
    
    Args:
        request: 更新权限请求数据
        current_user: 当前用户（必须是管理员）
        
    Returns:
        MessageResponse: 更新成功响应
    """
    try:
        # 获取目标用户信息
        target_user = await SupabaseAuthService.get_user_by_id(request.user_id)
        
        if not target_user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        # 更新用户权限
        updated_metadata = target_user.get("user_metadata", {}).copy()
        updated_metadata["permissions"] = request.permissions
        
        result = await SupabaseAuthService.update_user_metadata(
            user_id=request.user_id,
            metadata=updated_metadata
        )
        
        logger.info(f"User permissions updated by admin {current_user.email}: {request.user_id} -> {request.permissions}")
        
        return MessageResponse(message="User permissions updated successfully")
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Update user permissions failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to update user permissions: {str(e)}"
        )


@router.delete("/{user_id}", response_model=MessageResponse)
async def delete_user(
    user_id: str,
    current_user: SupabaseUser = Depends(require_admin())
):
    """
    删除用户（仅管理员）
    
    Args:
        user_id: 要删除的用户ID
        current_user: 当前用户（必须是管理员）
        
    Returns:
        MessageResponse: 删除成功响应
    """
    try:
        from backend.app.core.supabase_client import get_supabase_service_client
        
        # 防止管理员删除自己
        if user_id == current_user.id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Cannot delete your own account"
            )
        
        supabase = get_supabase_service_client()
        
        # 删除用户
        supabase.auth.admin.delete_user(user_id)
        
        logger.info(f"User deleted by admin {current_user.email}: {user_id}")
        
        return MessageResponse(message="User deleted successfully")
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Delete user failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to delete user: {str(e)}"
        )
