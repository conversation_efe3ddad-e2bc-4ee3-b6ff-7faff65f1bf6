# app/api/v2/controllers/auth/__init__.py

"""
V2 认证模块路由

基于 Supabase Auth 的认证系统，提供现代化的用户管理功能。
"""

from fastapi import APIRouter

from .auth import router as auth_main_router
from .users import router as users_router

# 创建认证模块主路由
auth_router = APIRouter()

# 注册认证相关路由
auth_router.include_router(
    auth_main_router,
    tags=["V2 Authentication - Core"]
)

auth_router.include_router(
    users_router,
    prefix="/users",
    tags=["V2 Authentication - User Management"]
)

__all__ = ["auth_router"]
