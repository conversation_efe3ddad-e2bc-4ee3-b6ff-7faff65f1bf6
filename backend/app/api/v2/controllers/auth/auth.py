# app/api/v2/controllers/auth/auth.py

"""
V2 认证相关 API

基于 Supabase Auth 的新一代认证系统。
提供用户注册、登录、登出、密码重置等现代化认证功能。
"""

from fastapi import APIRouter, HTTPException, status, Response, Cookie, Depends
from pydantic import BaseModel, EmailStr, Field
from typing import Optional, Dict, Any
from loguru import logger

from backend.app.services.supabase_auth_service import SupabaseAuthService
from backend.app.core.supabase_auth_deps import get_current_user, SupabaseUser


router = APIRouter(
    responses={401: {"description": "Unauthorized"}},
)


# Pydantic 模型
class SignUpRequest(BaseModel):
    """用户注册请求模型"""
    email: EmailStr = Field(..., description="用户邮箱")
    password: str = Field(..., min_length=6, description="用户密码")
    username: Optional[str] = Field(None, description="用户名（可选）")
    full_name: Optional[str] = Field(None, description="全名（可选）")


class SignInRequest(BaseModel):
    """用户登录请求模型"""
    email: EmailStr = Field(..., description="用户邮箱")
    password: str = Field(..., description="用户密码")


class ResetPasswordRequest(BaseModel):
    """密码重置请求模型"""
    email: EmailStr = Field(..., description="用户邮箱")


class AuthResponse(BaseModel):
    """认证响应模型"""
    access_token: str = Field(..., description="访问令牌")
    refresh_token: str = Field(..., description="刷新令牌")
    expires_at: int = Field(..., description="过期时间戳")
    token_type: str = Field(default="bearer", description="令牌类型")
    user: Dict[str, Any] = Field(..., description="用户信息")


class MessageResponse(BaseModel):
    """消息响应模型"""
    message: str = Field(..., description="响应消息")


@router.post("/signup", response_model=AuthResponse)
async def sign_up(request: SignUpRequest):
    """
    用户注册
    
    Args:
        request: 注册请求数据
        
    Returns:
        AuthResponse: 注册成功响应
    """
    try:
        # 准备用户元数据
        user_metadata = {}
        if request.username:
            user_metadata["username"] = request.username
        if request.full_name:
            user_metadata["full_name"] = request.full_name
        
        # 默认角色和权限
        user_metadata["roles"] = ["user"]
        user_metadata["permissions"] = ["read"]
        
        # 执行注册
        result = await SupabaseAuthService.sign_up(
            email=request.email,
            password=request.password,
            user_metadata=user_metadata
        )
        
        if result["session"]:
            return AuthResponse(
                access_token=result["session"].access_token,
                refresh_token=result["session"].refresh_token,
                expires_at=result["session"].expires_at,
                user=result["user"].__dict__
            )
        else:
            # 如果需要邮箱验证，session 可能为空
            raise HTTPException(
                status_code=status.HTTP_201_CREATED,
                detail=result["message"]
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Sign up failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Registration failed: {str(e)}"
        )


@router.post("/signin", response_model=AuthResponse)
async def sign_in(request: SignInRequest, response: Response):
    """
    用户登录
    
    Args:
        request: 登录请求数据
        response: HTTP 响应对象
        
    Returns:
        AuthResponse: 登录成功响应
    """
    try:
        # 执行登录
        result = await SupabaseAuthService.sign_in(
            email=request.email,
            password=request.password
        )
        
        # 设置 HttpOnly Cookie 存储 refresh token
        response.set_cookie(
            key="refresh_token",
            value=result["refresh_token"],
            max_age=7 * 24 * 60 * 60,  # 7天
            httponly=True,  # 防止 XSS
            secure=False,   # 开发环境设为False，生产环境应设为True
            samesite="lax", # CSRF 保护
            path="/"        # 确保 cookie 在整个域名下可用
        )
        
        return AuthResponse(
            access_token=result["access_token"],
            refresh_token=result["refresh_token"],
            expires_at=result["expires_at"],
            user=result["user"].__dict__
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Sign in failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid email or password"
        )


@router.post("/signout", response_model=MessageResponse)
async def sign_out(
    response: Response,
    current_user: SupabaseUser = Depends(get_current_user),
    refresh_token_cookie: Optional[str] = Cookie(None, alias="refresh_token")
):
    """
    用户登出
    
    Args:
        response: HTTP 响应对象
        current_user: 当前用户
        refresh_token_cookie: 刷新令牌 Cookie
        
    Returns:
        MessageResponse: 登出成功响应
    """
    try:
        # 清除 refresh token cookie
        response.delete_cookie(
            key="refresh_token",
            path="/",
            httponly=True,
            secure=False,
            samesite="lax"
        )
        
        logger.info(f"User signed out: {current_user.email}")
        
        return MessageResponse(message="Signed out successfully")
        
    except Exception as e:
        logger.error(f"Sign out failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Sign out failed: {str(e)}"
        )


@router.post("/refresh", response_model=AuthResponse)
async def refresh_token(
    refresh_token_cookie: Optional[str] = Cookie(None, alias="refresh_token")
):
    """
    刷新访问令牌
    
    Args:
        refresh_token_cookie: 刷新令牌 Cookie
        
    Returns:
        AuthResponse: 新的令牌信息
    """
    if not refresh_token_cookie:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Refresh token not found"
        )
    
    try:
        # 刷新会话
        result = await SupabaseAuthService.refresh_session(refresh_token_cookie)
        
        return AuthResponse(
            access_token=result["access_token"],
            refresh_token=result["refresh_token"],
            expires_at=result["expires_at"],
            user={}  # 刷新时不返回完整用户信息
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Token refresh failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Token refresh failed"
        )


@router.post("/reset-password", response_model=MessageResponse)
async def reset_password(request: ResetPasswordRequest):
    """
    发送密码重置邮件
    
    Args:
        request: 密码重置请求数据
        
    Returns:
        MessageResponse: 重置邮件发送成功响应
    """
    try:
        result = await SupabaseAuthService.reset_password(request.email)
        return MessageResponse(message=result["message"])
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Password reset failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Password reset failed: {str(e)}"
        )


@router.get("/me")
async def get_current_user_info(current_user: SupabaseUser = Depends(get_current_user)):
    """
    获取当前用户信息
    
    Args:
        current_user: 当前用户
        
    Returns:
        Dict: 用户信息
    """
    return {
        "id": current_user.id,
        "email": current_user.email,
        "user_metadata": current_user.user_metadata,
        "roles": current_user.roles,
        "permissions": current_user.permissions,
        "created_at": current_user.created_at,
        "last_sign_in_at": current_user.last_sign_in_at
    }
