# app/api/v2/controllers/douyin/videos.py

"""
V2 抖音视频管理 API

基于 Supabase Auth 的抖音视频管理功能。
使用 Row Level Security (RLS) 确保用户只能访问自己的数据。
"""

from fastapi import APIRouter, HTTPException, status, Depends, Query
from pydantic import BaseModel, Field, HttpUrl
from typing import Optional, List, Dict, Any
from loguru import logger

from backend.app.core.supabase_auth_deps import get_current_user, SupabaseUser
from backend.app.core.deps import AsyncSessionDep
from backend.app.models.douyin_models import Douyin
from backend.app.repo.douyin_repository import DouyinRepository
from backend.app.config.enums import DownloadStatus
from sqlalchemy import select, and_


router = APIRouter(
    responses={401: {"description": "Unauthorized"}},
)


# Pydantic 模型
class VideoCreateRequest(BaseModel):
    """视频创建请求模型"""
    video_original_url: HttpUrl = Field(..., description="视频原始URL")
    video_categories: Optional[str] = Field(None, description="视频分类")
    need_download_video: bool = Field(default=True, description="是否需要下载视频")
    need_download_music: bool = Field(default=False, description="是否需要下载音频")


class VideoResponse(BaseModel):
    """视频响应模型"""
    id: int = Field(..., description="视频ID")
    aweme_id: str = Field(..., description="抖音视频ID")
    video_original_url: str = Field(..., description="视频原始URL")
    video_title: Optional[str] = Field(None, description="视频标题")
    video_desc: Optional[str] = Field(None, description="视频描述")
    author: Optional[str] = Field(None, description="作者")
    video_duration: Optional[str] = Field(None, description="视频时长")
    video_resolution: Optional[str] = Field(None, description="视频分辨率")
    video_download_status: DownloadStatus = Field(..., description="下载状态")
    video_categories: Optional[str] = Field(None, description="视频分类")
    created_at: str = Field(..., description="创建时间")
    updated_at: str = Field(..., description="更新时间")
    
    class Config:
        from_attributes = True


class VideoListResponse(BaseModel):
    """视频列表响应模型"""
    videos: List[VideoResponse] = Field(..., description="视频列表")
    total: int = Field(..., description="总数")
    page: int = Field(..., description="当前页")
    per_page: int = Field(..., description="每页数量")


@router.post("/", response_model=VideoResponse)
async def create_video(
    request: VideoCreateRequest,
    current_user: SupabaseUser = Depends(get_current_user),
    db: AsyncSessionDep
):
    """
    创建新的视频记录
    
    Args:
        request: 视频创建请求
        current_user: 当前用户
        db: 数据库会话
        
    Returns:
        VideoResponse: 创建的视频信息
    """
    try:
        # 创建视频记录
        video_data = {
            "video_original_url": str(request.video_original_url),
            "video_categories": request.video_categories,
            "need_download_video": request.need_download_video,
            "need_download_music": request.need_download_music,
            "video_download_status": DownloadStatus.PENDING,
            "music_download_status": DownloadStatus.SKIPPED if not request.need_download_music else DownloadStatus.PENDING,
            # 在 Supabase 中，我们将用户ID存储在记录中以支持 RLS
            "user_id": current_user.id,  # 这需要在模型中添加
            # 临时使用 URL 作为 aweme_id，实际应该从 URL 解析
            "aweme_id": str(request.video_original_url).split("/")[-1] or "temp_id"
        }
        
        repo = DouyinRepository(db, Douyin)
        video = await repo.create(video_data)
        await db.commit()
        
        logger.info(f"Video created by user {current_user.email}: {video.id}")
        
        return VideoResponse.from_orm(video)
        
    except Exception as e:
        await db.rollback()
        logger.error(f"Create video failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to create video: {str(e)}"
        )


@router.get("/", response_model=VideoListResponse)
async def list_videos(
    current_user: SupabaseUser = Depends(get_current_user),
    db: AsyncSessionDep,
    page: int = Query(1, ge=1, description="页码"),
    per_page: int = Query(20, ge=1, le=100, description="每页数量"),
    status_filter: Optional[DownloadStatus] = Query(None, description="按状态筛选"),
    category_filter: Optional[str] = Query(None, description="按分类筛选")
):
    """
    获取当前用户的视频列表
    
    Args:
        current_user: 当前用户
        db: 数据库会话
        page: 页码
        per_page: 每页数量
        status_filter: 状态筛选
        category_filter: 分类筛选
        
    Returns:
        VideoListResponse: 视频列表和分页信息
    """
    try:
        # 构建查询条件
        conditions = []
        
        # 在实际实现中，这里应该使用 RLS 策略自动过滤用户数据
        # 暂时使用手动过滤（需要在模型中添加 user_id 字段）
        # conditions.append(Douyin.user_id == current_user.id)
        
        if status_filter:
            conditions.append(Douyin.video_download_status == status_filter)
        
        if category_filter:
            conditions.append(Douyin.video_categories == category_filter)
        
        # 构建查询
        query = select(Douyin)
        if conditions:
            query = query.where(and_(*conditions))
        
        # 分页
        offset = (page - 1) * per_page
        query = query.offset(offset).limit(per_page)
        
        # 执行查询
        result = await db.execute(query)
        videos = result.scalars().all()
        
        # 获取总数（简化实现）
        total_query = select(Douyin)
        if conditions:
            total_query = total_query.where(and_(*conditions))
        
        total_result = await db.execute(total_query)
        total = len(total_result.scalars().all())
        
        # 转换为响应模型
        video_responses = [VideoResponse.from_orm(video) for video in videos]
        
        return VideoListResponse(
            videos=video_responses,
            total=total,
            page=page,
            per_page=per_page
        )
        
    except Exception as e:
        logger.error(f"List videos failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list videos: {str(e)}"
        )


@router.get("/{video_id}", response_model=VideoResponse)
async def get_video(
    video_id: int,
    current_user: SupabaseUser = Depends(get_current_user),
    db: AsyncSessionDep
):
    """
    获取指定视频详情
    
    Args:
        video_id: 视频ID
        current_user: 当前用户
        db: 数据库会话
        
    Returns:
        VideoResponse: 视频详情
    """
    try:
        repo = DouyinRepository(db, Douyin)
        video = await repo.get_by_id(video_id)
        
        if not video:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Video not found"
            )
        
        # 在实际实现中，RLS 策略会自动确保用户只能访问自己的数据
        # 这里暂时跳过用户权限检查
        
        return VideoResponse.from_orm(video)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Get video failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get video: {str(e)}"
        )


@router.delete("/{video_id}")
async def delete_video(
    video_id: int,
    current_user: SupabaseUser = Depends(get_current_user),
    db: AsyncSessionDep
):
    """
    删除指定视频
    
    Args:
        video_id: 视频ID
        current_user: 当前用户
        db: 数据库会话
        
    Returns:
        Dict: 删除成功响应
    """
    try:
        repo = DouyinRepository(db, Douyin)
        
        # 检查视频是否存在
        video = await repo.get_by_id(video_id)
        if not video:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Video not found"
            )
        
        # 删除视频
        success = await repo.delete(video_id)
        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Failed to delete video"
            )
        
        await db.commit()
        
        logger.info(f"Video deleted by user {current_user.email}: {video_id}")
        
        return {"message": "Video deleted successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        logger.error(f"Delete video failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete video: {str(e)}"
        )
