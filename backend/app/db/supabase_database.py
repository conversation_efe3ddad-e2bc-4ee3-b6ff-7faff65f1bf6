# app/db/supabase_database.py

"""
Supabase 数据库连接和会话管理模块

提供 Supabase PostgreSQL 数据库的异步连接和会话工厂。
可以与现有的 SQLAlchemy 模型无缝集成。
"""

from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker
from loguru import logger

from backend.app.config.env import supabase_settings


def create_supabase_database_url() -> str:
    """
    创建 Supabase 数据库连接 URL
    
    Returns:
        str: Supabase PostgreSQL 连接 URL
    """
    return (
        f'postgresql+asyncpg://{supabase_settings.supabase_db_username}:'
        f'{supabase_settings.supabase_db_password}@'
        f'{supabase_settings.supabase_db_host}:{supabase_settings.supabase_db_port}/'
        f'{supabase_settings.supabase_db_database}'
    )


# Supabase 异步引擎
supabase_async_engine = create_async_engine(
    create_supabase_database_url(),
    echo=False,  # 禁用 SQL 日志
    pool_pre_ping=True,  # 连接检查机制
    future=True,
    # 添加异步相关配置
    pool_size=20,
    max_overflow=0,
    pool_recycle=3600,
    # 确保正确的异步上下文
    connect_args={
        "server_settings": {
            "application_name": "FastAPI_Supabase_App",
        }
    }
)

# Supabase 异步会话工厂
SupabaseAsyncSessionLocal = async_sessionmaker(
    bind=supabase_async_engine,
    expire_on_commit=False,
    class_=AsyncSession,
    # 确保异步上下文正确
    autoflush=True,
    autocommit=False
)


async def test_supabase_connection():
    """
    测试 Supabase 数据库连接
    
    Returns:
        bool: 连接成功返回 True，失败返回 False
    """
    try:
        async with supabase_async_engine.begin() as conn:
            result = await conn.execute("SELECT 1")
            logger.success("Supabase database connection successful")
            return True
    except Exception as e:
        logger.error(f"Supabase database connection failed: {e}")
        return False


def get_supabase_database_info():
    """
    获取 Supabase 数据库连接信息（用于调试）
    
    Returns:
        dict: 数据库连接信息
    """
    return {
        "host": supabase_settings.supabase_db_host,
        "port": supabase_settings.supabase_db_port,
        "database": supabase_settings.supabase_db_database,
        "username": supabase_settings.supabase_db_username,
        "url": create_supabase_database_url().replace(supabase_settings.supabase_db_password, "***")
    }
