# app/core/env.py

"""
应用程序配置模块

包含应用程序的各种配置参数，如日志设置、数据库连接、API密钥等。
这些设置可以通过环境变量覆盖，以支持不同的部署环境。
"""

import argparse
import os
import sys
from pathlib import Path
from typing import ClassVar, Optional , Literal
from functools import lru_cache

from pydantic import Field, computed_field, BaseModel, AliasChoices
from pydantic_settings import BaseSettings, SettingsConfigDict
from pydantic_settings.sources import YamlConfigSettingsSource

# 基础配置类，包含通用的配置加载逻辑
class BaseConfigSettings(BaseSettings):
    """基础配置类，包含通用的配置加载逻辑"""

    # 项目根目录 - 使用 ClassVar 标记为类变量，而不是模型字段
    root_path: ClassVar[Path] = Path(__file__).parent.parent.parent.parent
    # backend 目录路径 - .env 和 config.yml 都在 backend 文件夹中
    backend_path: ClassVar[Path] = Path(__file__).parent.parent.parent

    model_config = SettingsConfigDict(
        env_file=str(backend_path / '.env'),
        env_file_encoding='utf-8',
        # 在运行时赋值时验证字段值，确保类型安全
        validate_assignment=True,
        # 环境变量名称是否区分大小写
        case_sensitive=True,
        # 忽略额外字段
        extra='ignore'
    )

    @classmethod
    def settings_customise_sources(
        cls,
        settings_cls,  # Settings 类本身
        init_settings,  # 初始化时传入的参数
        env_settings,  # 环境变量设置源
        dotenv_settings,  # .env 文件设置源
        file_secret_settings,  # 文件密钥设置源（如 Docker secrets）
    ):
        """
        自定义配置源加载顺序
        优先级：环境变量 > .env 文件 > config.yml 文件 > 默认值
        """
        yaml_file_path = cls.backend_path / "config.yml"

        return (
            init_settings,
            env_settings,
            dotenv_settings,
            YamlConfigSettingsSource(settings_cls, yaml_file=yaml_file_path),
            file_secret_settings,
        )


class AppSettings(BaseConfigSettings):
    """
    应用程序基本设置
    """
    
    app_env: str = Field(default="dev", description="应用程序环境")
    app_name: str = Field(default="douyin推送下载app_config", description="抖音推送下载")


    app_host: str = Field(default="0.0.0.0", description="服务器监听的主机地址")
    app_port: int = Field(default=9889, description="应用程序端口")
    app_version: str = Field(default='1.0.0', description="应用程序版本")
    app_reload: bool = Field(default=False, description="是否启用热重载")
    app_workers: int = Field(default=5, description="工作进程数量")
    app_ip_location_query: bool = Field(default=True, description="是否启用IP位置查询")
    app_same_time_login: bool = Field(default=True, description="是否允许同时登录")
    app_nas_base_path: str = Field(default="/app/videos", description="NAS上存储抖音视频的基础路径")
    app_init_base_data_on_startup: bool = Field(default=True, description="是否在应用启动时自动初始化基础数据（角色、权限、管理员用户）")
    app_user_agents: list[str] = Field(default_factory=lambda: [
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
    ], description="用户代理字符串列表")

    # CORS设置（从 config.yml 加载结构化配置）
    app_cors_origins: list[str] = Field(default=["*"], description="允许的跨域来源")
    app_cors_credentials: bool = Field(default=True, description="是否允许凭证")

    # HTTP客户端设置（从 config.yml 加载）
    app_http_timeout: float = Field(default=30.0, description="HTTP请求超时时间(秒)")
    app_download_timeout: float = Field(default=30.0, description="下载超时时间(秒)")



#嵌套模型
class RedisSettings(BaseConfigSettings):
    """Redis配置模型"""
    redis_host: str = Field(default="localhost", description="Redis服务器地址", alias="REDIS_HOST")
    redis_port: int = Field(default=6379, description="Redis服务器端口", alias="REDIS_PORT")
    redis_db: int = Field(default=0, description="Redis数据库编号", alias="REDIS_DB")
    redis_username: Optional[str] = Field(default=None, description="Redis用户名", alias="REDIS_USERNAME")
    redis_password: Optional[str] = Field(default=None, description="Redis密码", alias="REDIS_PASSWORD")
    redis_decode_responses: bool = Field(default=True, description="是否解码响应", alias="REDIS_DECODE_RESPONSES")
    redis_max_connections: int = Field(default=20, description="最大连接数", alias="REDIS_MAX_CONNECTIONS")
    redis_socket_connect_timeout: int = Field(default=5, description="连接超时时间(秒)", alias="REDIS_SOCKET_CONNECT_TIMEOUT")
    redis_socket_timeout: int = Field(default=5, description="套接字超时时间(秒)", alias="REDIS_SOCKET_TIMEOUT")
    redis_retry_on_timeout: bool = Field(default=True, description="超时时是否重试", alias="REDIS_RETRY_ON_TIMEOUT")
    redis_health_check_interval: int = Field(default=30, description="健康检查间隔(秒)", alias="REDIS_HEALTH_CHECK_INTERVAL")

class JwtSettings(BaseConfigSettings):
    """
    JWT配置
    """
    # JWT 设置（从 .env 加载敏感信息）
    jwt_secret_key: str = Field(default="please-change-this-secret-key", description="JWT密钥", alias="JWT_SECRET_KEY")
    jwt_algorithm: str = Field(default="HS256", description="JWT算法", alias="JWT_ALGORITHM")
    jwt_access_token_expire_time: int = Field(default=30, description="访问令牌过期时间(分钟)", alias="JWT_ACCESS_TOKEN_EXPIRE_TIME")
    jwt_refresh_token_expire_time: int = Field(default=10080, description="刷新令牌过期时间(分钟)", alias="JWT_REFRESH_TOKEN_EXPIRE_TIME")

class DataBaseSettings(BaseConfigSettings):
    """
    数据库配置
    """

    db_type: Literal['mysql', 'postgresql'] = Field(
        default='postgresql',
        description="数据库类型，支持 mysql 或 postgresql"
    )
    db_host: str = Field(default='localhost', description="数据库服务器地址", alias="DB_HOST")
    db_port: int = Field(default=5432, description="数据库端口", alias="DB_PORT")
    db_username: str = Field(default='your_username', description="数据库用户名", alias="DB_USERNAME")
    db_password: str = Field(default='your_password', description="数据库密码", alias="DB_PASSWORD")
    db_database: str = Field(default='your_database', description="数据库名称", alias="DB_DATABASE")
    db_push_to_db: bool = Field(default=True, description="是否推送数据到数据库", alias="DB_PUSH_TO_DB")
    db_echo: bool = Field(default=False, description="是否打印SQL语句", alias="DB_ECHO")
    db_max_overflow: int = Field(default=10, description="连接池最大溢出连接数", alias="DB_MAX_OVERFLOW")
    db_pool_size: int = Field(default=20, description="连接池大小", alias="DB_POOL_SIZE")
    db_pool_recycle: int = Field(default=3600, description="连接回收时间(秒)", alias="DB_POOL_RECYCLE")
    db_pool_timeout: int = Field(default=30, description="连接池获取连接超时时间(秒)", alias="DB_POOL_TIMEOUT")


class NotionSettings(BaseConfigSettings):
    """Notion API设置"""
    # （从 .env 加载敏感信息）
    notion_api_key: str = Field(default="", description="Notion API密钥", alias="NOTION_API_KEY")
    notion_database_id: str = Field(default="", description="Notion数据库ID", alias="NOTION_DATABASE_ID")
    notion_push_to_notion: bool = Field(default=False, description="是否自动推送到Notion", alias="NOTION_PUSH_TO_NOTION")


class SupabaseSettings(BaseConfigSettings):
    """Supabase配置"""
    # Supabase 项目配置（从 .env 加载敏感信息）
    supabase_url: str = Field(default="", description="Supabase项目URL", alias="SUPABASE_URL")
    supabase_anon_key: str = Field(default="", description="Supabase匿名密钥", alias="SUPABASE_ANON_KEY")
    supabase_service_role_key: str = Field(default="", description="Supabase服务角色密钥", alias="SUPABASE_SERVICE_ROLE_KEY")

    # 认证配置
    supabase_jwt_secret: str = Field(default="", description="Supabase JWT密钥", alias="SUPABASE_JWT_SECRET")
    supabase_auth_redirect_url: str = Field(default="http://localhost:3000/auth/callback", description="认证重定向URL", alias="SUPABASE_AUTH_REDIRECT_URL")

    # 可选配置
    supabase_auth_auto_confirm: bool = Field(default=True, description="是否自动确认用户", alias="SUPABASE_AUTH_AUTO_CONFIRM")
    supabase_auth_enable_signup: bool = Field(default=True, description="是否启用用户注册", alias="SUPABASE_AUTH_ENABLE_SIGNUP")

    # Supabase 数据库配置（用于直接数据库连接）
    supabase_db_host: str = Field(default="", description="Supabase数据库主机", alias="SUPABASE_DB_HOST")
    supabase_db_port: int = Field(default=5432, description="Supabase数据库端口", alias="SUPABASE_DB_PORT")
    supabase_db_username: str = Field(default="postgres", description="Supabase数据库用户名", alias="SUPABASE_DB_USERNAME")
    supabase_db_password: str = Field(default="", description="Supabase数据库密码", alias="SUPABASE_DB_PASSWORD")
    supabase_db_database: str = Field(default="postgres", description="Supabase数据库名称", alias="SUPABASE_DB_DATABASE")






class GetConfig:
    """配置管理类，负责统一管理所有配置"""

    def __init__(self):
        pass

    @lru_cache()
    def get_app_config(self):
        """获取应用配置"""
        return AppSettings()

    @lru_cache()
    def get_jwt_config(self):
        """获取JWT配置"""
        return JwtSettings()

    @lru_cache()
    def get_database_config(self):
        """获取数据库配置"""
        return DataBaseSettings()

    @lru_cache()
    def get_redis_config(self):
        """获取Redis配置"""
        return RedisSettings()

    @lru_cache()
    def get_notion_config(self):
        """获取Notion配置"""
        return NotionSettings()

    @lru_cache()
    def get_supabase_config(self):
        """获取Supabase配置"""
        return SupabaseSettings()






# 创建全局配置实例
config = GetConfig()

# 导出各个配置实例，方便其他模块使用
app_settings = config.get_app_config()
jwt_settings = config.get_jwt_config()
database_settings = config.get_database_config()
redis_settings = config.get_redis_config()
notion_settings = config.get_notion_config()
supabase_settings = config.get_supabase_config()


