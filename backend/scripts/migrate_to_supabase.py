# scripts/migrate_to_supabase.py

"""
数据迁移脚本：从现有认证系统迁移到 Supabase Auth

此脚本将现有的用户、角色、权限数据迁移到 Supabase Auth 系统。
"""

import asyncio
import sys
from pathlib import Path
from typing import List, Dict, Any
from loguru import logger

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from backend.app.db.session import get_async_session
from backend.app.db.supabase_database import test_supabase_connection, SupabaseAsyncSessionLocal
from backend.app.models.user_models import User, Role, Permission
from backend.app.services.supabase_auth_service import SupabaseAuthService
from backend.app.core.supabase_client import get_supabase_service_client
from backend.app.repo.user_repository import UserRepository, RoleRepository, PermissionRepository
from sqlalchemy import select
from sqlalchemy.orm import selectinload


class SupabaseMigrationService:
    """Supabase 迁移服务"""
    
    def __init__(self):
        self.supabase = get_supabase_service_client()
        self.migration_stats = {
            "users_migrated": 0,
            "users_failed": 0,
            "roles_processed": 0,
            "permissions_processed": 0,
            "errors": []
        }
    
    async def migrate_users(self) -> Dict[str, Any]:
        """
        迁移用户数据到 Supabase Auth
        
        Returns:
            Dict: 迁移统计信息
        """
        logger.info("Starting user migration to Supabase Auth...")
        
        try:
            # 连接到现有数据库
            async for db in get_async_session():
                try:
                    # 获取所有用户及其角色和权限
                    query = (
                        select(User)
                        .options(
                            selectinload(User.roles).selectinload(Role.permissions)
                        )
                    )
                    result = await db.execute(query)
                    users = result.scalars().all()
                    
                    logger.info(f"Found {len(users)} users to migrate")
                    
                    for user in users:
                        await self._migrate_single_user(user)
                    
                    break  # 只使用一个数据库会话
                    
                except Exception as e:
                    logger.error(f"Database query failed: {e}")
                    self.migration_stats["errors"].append(f"Database query failed: {e}")
                    
        except Exception as e:
            logger.error(f"Migration failed: {e}")
            self.migration_stats["errors"].append(f"Migration failed: {e}")
        
        # 输出迁移统计
        logger.info("Migration completed!")
        logger.info(f"Users migrated: {self.migration_stats['users_migrated']}")
        logger.info(f"Users failed: {self.migration_stats['users_failed']}")
        logger.info(f"Roles processed: {self.migration_stats['roles_processed']}")
        logger.info(f"Permissions processed: {self.migration_stats['permissions_processed']}")
        
        if self.migration_stats["errors"]:
            logger.error("Migration errors:")
            for error in self.migration_stats["errors"]:
                logger.error(f"  - {error}")
        
        return self.migration_stats
    
    async def _migrate_single_user(self, user: User) -> bool:
        """
        迁移单个用户
        
        Args:
            user: 用户对象
            
        Returns:
            bool: 迁移是否成功
        """
        try:
            # 收集用户的角色和权限
            roles = []
            permissions = set()
            
            for role in user.roles:
                roles.append(role.name.value)
                self.migration_stats["roles_processed"] += 1
                
                for permission in role.permissions:
                    permissions.add(permission.name.value)
                    self.migration_stats["permissions_processed"] += 1
            
            # 准备用户元数据
            user_metadata = {
                "username": user.username,
                "roles": roles,
                "permissions": list(permissions),
                "migrated_from": "legacy_system",
                "original_user_id": user.id,
                "created_at": user.created_at.isoformat() if user.created_at else None,
                "updated_at": user.updated_at.isoformat() if user.updated_at else None
            }
            
            # 生成临时邮箱（如果用户名不是邮箱格式）
            email = user.username if "@" in user.username else f"{user.username}@migrated.local"
            
            # 生成临时密码（用户需要重置）
            temp_password = f"temp_{user.username}_password_123!"
            
            # 在 Supabase Auth 中创建用户
            response = self.supabase.auth.admin.create_user({
                "email": email,
                "password": temp_password,
                "user_metadata": user_metadata,
                "email_confirm": True  # 自动确认邮箱
            })
            
            if response.user:
                logger.info(f"Successfully migrated user: {user.username} -> {email}")
                self.migration_stats["users_migrated"] += 1
                return True
            else:
                logger.error(f"Failed to create user in Supabase: {user.username}")
                self.migration_stats["users_failed"] += 1
                self.migration_stats["errors"].append(f"Failed to create user: {user.username}")
                return False
                
        except Exception as e:
            logger.error(f"Failed to migrate user {user.username}: {e}")
            self.migration_stats["users_failed"] += 1
            self.migration_stats["errors"].append(f"User {user.username}: {e}")
            return False
    
    async def setup_rls_policies(self):
        """
        设置 Row Level Security 策略
        
        这个方法会在 Supabase 数据库中创建必要的 RLS 策略
        """
        logger.info("Setting up Row Level Security policies...")
        
        try:
            # 连接到 Supabase 数据库
            async with SupabaseAsyncSessionLocal() as db:
                # 这里可以执行 SQL 来创建 RLS 策略
                # 例如：为 douyin_data 表创建策略
                
                rls_policies = [
                    # 用户只能访问自己的数据
                    """
                    CREATE POLICY "Users can view own data" ON douyin.douyin_data
                    FOR SELECT USING (auth.uid()::text = user_id);
                    """,
                    
                    # 用户只能插入自己的数据
                    """
                    CREATE POLICY "Users can insert own data" ON douyin.douyin_data
                    FOR INSERT WITH CHECK (auth.uid()::text = user_id);
                    """,
                    
                    # 用户只能更新自己的数据
                    """
                    CREATE POLICY "Users can update own data" ON douyin.douyin_data
                    FOR UPDATE USING (auth.uid()::text = user_id);
                    """,
                    
                    # 管理员可以访问所有数据
                    """
                    CREATE POLICY "Admins can access all data" ON douyin.douyin_data
                    FOR ALL USING (
                        auth.jwt() ->> 'user_metadata' ->> 'roles' ? 'admin'
                    );
                    """
                ]
                
                for policy in rls_policies:
                    try:
                        await db.execute(policy)
                        logger.info("RLS policy created successfully")
                    except Exception as e:
                        # 策略可能已存在，忽略错误
                        logger.warning(f"RLS policy creation warning: {e}")
                
                await db.commit()
                
        except Exception as e:
            logger.error(f"Failed to setup RLS policies: {e}")
    
    async def verify_migration(self) -> Dict[str, Any]:
        """
        验证迁移结果
        
        Returns:
            Dict: 验证结果
        """
        logger.info("Verifying migration...")
        
        try:
            # 获取 Supabase 中的用户数量
            response = self.supabase.auth.admin.list_users()
            supabase_user_count = len(response.users) if response.users else 0
            
            # 获取原数据库中的用户数量
            async for db in get_async_session():
                try:
                    result = await db.execute(select(User))
                    original_user_count = len(result.scalars().all())
                    break
                except Exception as e:
                    logger.error(f"Failed to count original users: {e}")
                    original_user_count = 0
            
            verification_result = {
                "original_users": original_user_count,
                "supabase_users": supabase_user_count,
                "migration_success_rate": (
                    self.migration_stats["users_migrated"] / original_user_count * 100
                    if original_user_count > 0 else 0
                )
            }
            
            logger.info(f"Verification results: {verification_result}")
            return verification_result
            
        except Exception as e:
            logger.error(f"Migration verification failed: {e}")
            return {"error": str(e)}


async def main():
    """主函数"""
    logger.info("Starting Supabase migration process...")
    
    # 测试 Supabase 连接
    if not await test_supabase_connection():
        logger.error("Supabase connection test failed. Please check your configuration.")
        return
    
    # 创建迁移服务
    migration_service = SupabaseMigrationService()
    
    # 执行迁移
    migration_stats = await migration_service.migrate_users()
    
    # 设置 RLS 策略
    await migration_service.setup_rls_policies()
    
    # 验证迁移
    verification_result = await migration_service.verify_migration()
    
    logger.info("Migration process completed!")
    logger.info(f"Final stats: {migration_stats}")
    logger.info(f"Verification: {verification_result}")


if __name__ == "__main__":
    asyncio.run(main())
