# scripts/test_supabase_config.py

"""
Supabase 配置测试脚本

测试 Supabase 连接、认证功能和数据库访问。
"""

import asyncio
import sys
from pathlib import Path
from loguru import logger

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from backend.app.config.env import supabase_settings
from backend.app.core.supabase_client import get_supabase_anon_client, get_supabase_service_client
from backend.app.db.supabase_database import test_supabase_connection, get_supabase_database_info
from backend.app.services.supabase_auth_service import SupabaseAuthService


class SupabaseConfigTester:
    """Supabase 配置测试器"""
    
    def __init__(self):
        self.test_results = {
            "config_check": False,
            "anon_client": False,
            "service_client": False,
            "database_connection": False,
            "auth_signup": False,
            "auth_signin": False,
            "jwt_verification": False,
            "errors": []
        }
    
    def test_configuration(self) -> bool:
        """测试配置是否完整"""
        logger.info("Testing Supabase configuration...")
        
        required_configs = [
            ("SUPABASE_URL", supabase_settings.supabase_url),
            ("SUPABASE_ANON_KEY", supabase_settings.supabase_anon_key),
            ("SUPABASE_SERVICE_ROLE_KEY", supabase_settings.supabase_service_role_key),
            ("SUPABASE_JWT_SECRET", supabase_settings.supabase_jwt_secret),
        ]
        
        missing_configs = []
        for config_name, config_value in required_configs:
            if not config_value or config_value == "":
                missing_configs.append(config_name)
        
        if missing_configs:
            error_msg = f"Missing required configurations: {', '.join(missing_configs)}"
            logger.error(error_msg)
            self.test_results["errors"].append(error_msg)
            return False
        
        logger.success("Configuration check passed")
        self.test_results["config_check"] = True
        return True
    
    def test_anon_client(self) -> bool:
        """测试匿名客户端连接"""
        logger.info("Testing anonymous client connection...")
        
        try:
            client = get_supabase_anon_client()
            
            # 尝试获取用户（应该返回 None，但不应该抛出异常）
            user = client.auth.get_user()
            
            logger.success("Anonymous client connection successful")
            self.test_results["anon_client"] = True
            return True
            
        except Exception as e:
            error_msg = f"Anonymous client test failed: {e}"
            logger.error(error_msg)
            self.test_results["errors"].append(error_msg)
            return False
    
    def test_service_client(self) -> bool:
        """测试服务角色客户端连接"""
        logger.info("Testing service role client connection...")
        
        try:
            client = get_supabase_service_client()
            
            # 尝试列出用户（需要服务角色权限）
            response = client.auth.admin.list_users()
            
            logger.success(f"Service role client connection successful. Found {len(response.users) if response.users else 0} users")
            self.test_results["service_client"] = True
            return True
            
        except Exception as e:
            error_msg = f"Service role client test failed: {e}"
            logger.error(error_msg)
            self.test_results["errors"].append(error_msg)
            return False
    
    async def test_database_connection(self) -> bool:
        """测试数据库连接"""
        logger.info("Testing database connection...")
        
        try:
            # 显示数据库连接信息
            db_info = get_supabase_database_info()
            logger.info(f"Database info: {db_info}")
            
            # 测试连接
            connection_success = await test_supabase_connection()
            
            if connection_success:
                logger.success("Database connection successful")
                self.test_results["database_connection"] = True
                return True
            else:
                error_msg = "Database connection failed"
                logger.error(error_msg)
                self.test_results["errors"].append(error_msg)
                return False
                
        except Exception as e:
            error_msg = f"Database connection test failed: {e}"
            logger.error(error_msg)
            self.test_results["errors"].append(error_msg)
            return False
    
    async def test_auth_functionality(self) -> bool:
        """测试认证功能"""
        logger.info("Testing authentication functionality...")
        
        test_email = "<EMAIL>"
        test_password = "test123456"
        
        try:
            # 测试注册
            logger.info("Testing user registration...")
            signup_result = await SupabaseAuthService.sign_up(
                email=test_email,
                password=test_password,
                user_metadata={"test": True, "roles": ["user"]}
            )
            
            if signup_result:
                logger.success("User registration test passed")
                self.test_results["auth_signup"] = True
            
            # 测试登录
            logger.info("Testing user sign in...")
            signin_result = await SupabaseAuthService.sign_in(
                email=test_email,
                password=test_password
            )
            
            if signin_result and signin_result.get("access_token"):
                logger.success("User sign in test passed")
                self.test_results["auth_signin"] = True
                
                # 测试 JWT 验证
                logger.info("Testing JWT verification...")
                token_payload = SupabaseAuthService.verify_jwt_token(signin_result["access_token"])
                
                if token_payload and token_payload.get("sub"):
                    logger.success("JWT verification test passed")
                    self.test_results["jwt_verification"] = True
            
            # 清理测试用户
            try:
                service_client = get_supabase_service_client()
                if signin_result and signin_result.get("user"):
                    user_id = signin_result["user"].id
                    service_client.auth.admin.delete_user(user_id)
                    logger.info("Test user cleaned up")
            except Exception as cleanup_error:
                logger.warning(f"Failed to cleanup test user: {cleanup_error}")
            
            return True
            
        except Exception as e:
            error_msg = f"Authentication functionality test failed: {e}"
            logger.error(error_msg)
            self.test_results["errors"].append(error_msg)
            return False
    
    async def run_all_tests(self) -> Dict[str, Any]:
        """运行所有测试"""
        logger.info("Starting comprehensive Supabase configuration tests...")
        
        # 1. 配置检查
        self.test_configuration()
        
        # 2. 客户端连接测试
        self.test_anon_client()
        self.test_service_client()
        
        # 3. 数据库连接测试
        await self.test_database_connection()
        
        # 4. 认证功能测试
        if self.test_results["anon_client"] and self.test_results["service_client"]:
            await self.test_auth_functionality()
        else:
            logger.warning("Skipping auth functionality tests due to client connection failures")
        
        # 输出测试结果
        self._print_test_results()
        
        return self.test_results
    
    def _print_test_results(self):
        """打印测试结果"""
        logger.info("\n" + "="*50)
        logger.info("SUPABASE CONFIGURATION TEST RESULTS")
        logger.info("="*50)
        
        passed_tests = sum(1 for result in self.test_results.values() if isinstance(result, bool) and result)
        total_tests = sum(1 for result in self.test_results.values() if isinstance(result, bool))
        
        logger.info(f"Overall: {passed_tests}/{total_tests} tests passed")
        
        for test_name, result in self.test_results.items():
            if isinstance(result, bool):
                status = "✅ PASS" if result else "❌ FAIL"
                logger.info(f"{test_name}: {status}")
        
        if self.test_results["errors"]:
            logger.info("\nErrors encountered:")
            for error in self.test_results["errors"]:
                logger.error(f"  - {error}")
        
        if passed_tests == total_tests:
            logger.success("\n🎉 All tests passed! Supabase is ready for migration.")
        else:
            logger.warning(f"\n⚠️  {total_tests - passed_tests} test(s) failed. Please fix the issues before proceeding with migration.")


async def main():
    """主函数"""
    tester = SupabaseConfigTester()
    await tester.run_all_tests()


if __name__ == "__main__":
    asyncio.run(main())
