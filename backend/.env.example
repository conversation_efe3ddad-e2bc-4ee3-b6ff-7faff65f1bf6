# FastAPI Auth 应用配置示例
# 复制此文件为 .env 并根据需要修改配置

# 应用基本设置
APP_NAME="FastAPI Auth System"
HOST="0.0.0.0"
APP_PORT=8000
RELOAD=true

# 数据库配置
DATABASE_URL="postgresql+asyncpg://username:password@localhost:5432/fastapi_auth"

# JWT 配置
SECRET_KEY="your-super-secret-key-change-this-in-production"
ALGORITHM="HS256"
ACCESS_TOKEN_EXPIRE_TIME=30
REFRESH_TOKEN_EXPIRE_TIME=10080

# Redis 配置
REDIS_HOST="127.0.0.1"
REDIS_PORT=16379
REDIS_DB=0
REDIS_PASSWORD=""

# CORS 配置
CORS_ORIGINS=["http://localhost:3000", "http://localhost:8080"]
CORS_CREDENTIALS=true

# 启动时初始化设置
# 设置为 false 可以禁用启动时自动初始化基础数据（角色、权限、管理员用户）
INIT_BASE_DATA_ON_STARTUP=true

# 日志配置
LOG_LEVEL="INFO"
LOG_FILE="logs/app.log"

# Notion 集成（可选）
NOTION_API_KEY=""
NOTION_DATABASE_ID=""
PUSH_TO_NOTION=false

# ===========================================
# Supabase 配置（敏感信息）
# ===========================================
SUPABASE_URL="https://your-project.supabase.co"
SUPABASE_ANON_KEY="your-anon-key"
SUPABASE_SERVICE_ROLE_KEY="your-service-role-key"
SUPABASE_JWT_SECRET="your-jwt-secret"

# Supabase Auth 配置
SUPABASE_AUTH_REDIRECT_URL="http://localhost:3000/auth/callback"
SUPABASE_AUTH_AUTO_CONFIRM=true
SUPABASE_AUTH_ENABLE_SIGNUP=true

# 其他设置
HTTP_TIMEOUT=30.0
DOWNLOAD_TIMEOUT=30.0
