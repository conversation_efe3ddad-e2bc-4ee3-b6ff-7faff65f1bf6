# Supabase Auth 迁移指南

本指南将帮助你完成从现有 JWT 认证系统到 Supabase Auth 的完整迁移。

## 📋 迁移前准备

### 1. 确认 Supabase 服务状态

确保你的 Docker Supabase 实例正在运行：

```bash
# 检查 Supabase 容器状态
docker ps | grep supabase

# 访问 Supabase Studio
# http://************:3080
# 用户名: heygo
# 密码: Hey@go01!
```

### 2. 验证配置

运行配置测试脚本：

```bash
cd backend
python scripts/test_supabase_config.py
```

如果测试失败，请检查 `.env` 文件中的 Supabase 配置。

## 🚀 迁移步骤

### 步骤 1: 备份现有数据

```bash
# 备份现有数据库
pg_dump -h 127.0.0.1 -p 25432 -U heygo -d heygo > backup_before_migration.sql
```

### 步骤 2: 更新数据库连接

修改 `backend/app/db/database.py` 以使用 Supabase 数据库：

```python
# 替换现有的数据库连接为 Supabase
from backend.app.db.supabase_database import supabase_async_engine, SupabaseAsyncSessionLocal

# 使用 Supabase 引擎
async_engine = supabase_async_engine
AsyncSessionLocal = SupabaseAsyncSessionLocal
```

### 步骤 3: 执行数据迁移

```bash
cd backend
python scripts/migrate_to_supabase.py
```

这个脚本将：
- 迁移所有现有用户到 Supabase Auth
- 将角色和权限信息存储在用户元数据中
- 设置 Row Level Security (RLS) 策略

### 步骤 4: API 版本分离

现在你有两套完全独立的 API 版本：

**V1 API (传统认证系统):**
- `POST /v1/auth/login` - 传统登录
- `POST /v1/auth/logout` - 传统登出
- `GET /v1/auth/users/` - 用户管理
- 所有现有的 V1 端点保持不变

**V2 API (Supabase 认证系统):**
- `POST /v2/auth/signup` - 用户注册
- `POST /v2/auth/signin` - 用户登录
- `POST /v2/auth/signout` - 用户登出
- `POST /v2/auth/refresh` - 刷新令牌
- `POST /v2/auth/reset-password` - 密码重置
- `GET /v2/auth/me` - 获取当前用户信息
- `GET /v2/auth/users/profile` - 用户资料
- `PUT /v2/auth/users/profile` - 更新用户资料
- `GET /v2/auth/users/list` - 用户列表（管理员）
- `PUT /v2/auth/users/roles` - 更新用户角色（管理员）
- `DELETE /v2/auth/users/{user_id}` - 删除用户（管理员）
- `GET /v2/douyin/videos/` - 视频列表
- `POST /v2/douyin/videos/` - 创建视频
- `GET /v2/douyin/videos/{video_id}` - 视频详情
- `DELETE /v2/douyin/videos/{video_id}` - 删除视频

### 步骤 5: 更新前端代码

如果你有前端应用，需要更新认证逻辑：

```javascript
// V2 API 登录
const response = await fetch('/v2/auth/signin', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    email: '<EMAIL>',
    password: 'password123'
  })
});

const data = await response.json();
// 存储 access_token 用于后续请求
localStorage.setItem('access_token', data.access_token);

// V2 API 获取用户信息
const userResponse = await fetch('/v2/auth/me', {
  headers: {
    'Authorization': `Bearer ${localStorage.getItem('access_token')}`
  }
});

// V2 API 获取视频列表
const videosResponse = await fetch('/v2/douyin/videos/', {
  headers: {
    'Authorization': `Bearer ${localStorage.getItem('access_token')}`
  }
});
```

### 步骤 6: 更新认证中间件

在需要认证的 API 端点中，使用新的依赖：

```python
from backend.app.core.supabase_auth_deps import get_current_user, SupabaseUser, require_admin

@router.get("/protected-endpoint")
async def protected_endpoint(current_user: SupabaseUser = Depends(get_current_user)):
    return {"message": f"Hello {current_user.email}"}

@router.get("/admin-only")
async def admin_only(current_user: SupabaseUser = Depends(require_admin())):
    return {"message": "Admin access granted"}
```

## 🔧 配置说明

### 环境变量

确保 `.env` 文件包含以下配置：

```env
# Supabase 配置
SUPABASE_URL=http://************:8000
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
SUPABASE_JWT_SECRET=fS5UFPylUtABaSwqHNrnqJ5orodgiQzGhbh9ftUL

# Supabase 数据库配置
SUPABASE_DB_HOST=************
SUPABASE_DB_PORT=54325
SUPABASE_DB_USERNAME=postgres
SUPABASE_DB_PASSWORD=HeygoyG7!p2xWq9Vk5&JsZm8LuRd!
SUPABASE_DB_DATABASE=postgres
```

### 用户角色和权限

在新系统中，角色和权限存储在用户的 `user_metadata` 中：

```json
{
  "user_metadata": {
    "username": "admin",
    "roles": ["admin", "user"],
    "permissions": ["read", "write", "delete"],
    "migrated_from": "legacy_system"
  }
}
```

## 🧪 测试迁移

### 1. 测试新的认证 API

```bash
# 测试 V2 注册
curl -X POST http://localhost:9889/v2/auth/signup \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "password123", "username": "testuser"}'

# 测试 V2 登录
curl -X POST http://localhost:9889/v2/auth/signin \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "password123"}'

# 测试 V2 视频创建
curl -X POST http://localhost:9889/v2/douyin/videos/ \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -d '{"video_original_url": "https://www.douyin.com/video/123456", "video_categories": "entertainment"}'
```

### 2. 测试受保护的端点

```bash
# 使用获得的 access_token 测试 V2 API
curl -X GET http://localhost:9889/v2/auth/me \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"

# 测试 V2 视频列表
curl -X GET http://localhost:9889/v2/douyin/videos/ \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"

# 测试 V2 用户资料
curl -X GET http://localhost:9889/v2/auth/users/profile \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

## 🔄 回滚计划

如果迁移出现问题，可以快速回滚：

### 1. 恢复原有数据库连接

```python
# 在 backend/app/db/database.py 中恢复原有配置
ASYNC_SQLALCHEMY_DATABASE_URL = (
    f'postgresql+asyncpg://{database_settings.db_username}:'
    f'{database_settings.db_password}@'
    f'{database_settings.db_host}:{database_settings.db_port}/'
    f'{database_settings.db_database}'
)
```

### 2. 禁用 Supabase 路由

在 `backend/app/api/v1/controllers/auth/__init__.py` 中注释掉：

```python
# auth_router.include_router(
#     supabase_auth_router,
#     prefix="/supabase",
#     tags=["Supabase Authentication"]
# )
```

### 3. 恢复数据库

```bash
psql -h 127.0.0.1 -p 25432 -U heygo -d heygo < backup_before_migration.sql
```

## 📊 监控和日志

迁移后，监控以下指标：

1. **认证成功率**: 检查登录/注册是否正常工作
2. **API 响应时间**: Supabase Auth 的性能表现
3. **错误日志**: 查看是否有认证相关错误
4. **用户反馈**: 确保用户体验良好

## 🎯 迁移后的优势

完成迁移后，你将获得：

- ✅ 减少 80% 的认证相关代码维护
- ✅ 企业级安全性和合规性
- ✅ 内置用户管理界面
- ✅ 自动邮件验证和密码重置
- ✅ 支持社交登录扩展
- ✅ Row Level Security 自动权限控制
- ✅ 实时功能支持

## 🆘 故障排除

### 常见问题

1. **JWT 验证失败**
   - 检查 `SUPABASE_JWT_SECRET` 是否正确
   - 确认令牌格式和算法

2. **数据库连接失败**
   - 验证 Supabase 数据库配置
   - 检查网络连接和端口

3. **用户迁移失败**
   - 检查原数据库连接
   - 验证 Supabase 服务角色权限

如需帮助，请查看日志文件或联系技术支持。
