# V2 API 迁移策略

## 🎯 架构设计理念

通过创建 V2 API，我们实现了：

### ✅ 版本隔离
- **V1 API**: 保持现有的 JWT 认证系统不变
- **V2 API**: 全新的 Supabase Auth 认证系统
- **零影响**: 现有用户和系统继续正常工作

### ✅ 平滑迁移
- 可以逐步迁移用户到 V2 系统
- 支持 A/B 测试
- 可以随时回滚到 V1

### ✅ 功能增强
- V2 提供更现代化的认证体验
- 内置用户管理功能
- 更好的安全性和性能

## 📊 API 对比

| 功能 | V1 API | V2 API | 说明 |
|------|--------|--------|------|
| 用户注册 | ❌ | ✅ `POST /v2/auth/signup` | V2 支持自助注册 |
| 用户登录 | ✅ `POST /v1/auth/login` | ✅ `POST /v2/auth/signin` | 两套独立的登录系统 |
| 用户登出 | ✅ `POST /v1/auth/logout` | ✅ `POST /v2/auth/signout` | V2 支持更安全的登出 |
| 令牌刷新 | ✅ `POST /v1/auth/refresh` | ✅ `POST /v2/auth/refresh` | V2 使用 HttpOnly Cookie |
| 密码重置 | ❌ | ✅ `POST /v2/auth/reset-password` | V2 支持邮件重置 |
| 用户资料 | 部分支持 | ✅ `GET/PUT /v2/auth/users/profile` | V2 完整的资料管理 |
| 用户管理 | ✅ 复杂的角色系统 | ✅ 简化的角色系统 | V2 使用用户元数据 |
| 权限控制 | 基于数据库 | 基于 RLS + 元数据 | V2 更高效 |
| 视频管理 | ✅ `/v1/douyin/` | ✅ `/v2/douyin/videos/` | V2 支持用户隔离 |

## 🚀 迁移策略

### 阶段 1: 并行运行（当前阶段）
```
用户 A (现有) → V1 API → 传统认证 → PostgreSQL
用户 B (新用户) → V2 API → Supabase Auth → Supabase DB
```

**优势:**
- 零风险，现有系统不受影响
- 新用户可以体验 V2 的优势
- 可以收集 V2 的使用反馈

### 阶段 2: 逐步迁移
```
用户 A → 选择迁移到 V2 → 数据迁移脚本 → V2 API
用户 B → 继续使用 V2
用户 C (坚持) → 继续使用 V1
```

**实施方法:**
1. 提供用户迁移工具
2. 发送迁移邀请邮件
3. 提供迁移激励（新功能访问权限）

### 阶段 3: V1 弃用（未来）
```
所有用户 → V2 API → Supabase Auth → Supabase DB
V1 API → 只读模式 → 最终下线
```

## 🔧 技术实现

### 数据库策略
```sql
-- V1 数据库（保持不变）
PostgreSQL (127.0.0.1:25432)
├── users (传统用户表)
├── roles (角色表)
├── permissions (权限表)
└── douyin_data (视频数据)

-- V2 数据库（Supabase）
Supabase PostgreSQL (192.168.50.9:54325)
├── auth.users (Supabase 用户表)
├── douyin_data (带 RLS 的视频数据)
└── 其他业务表
```

### 认证流程
```python
# V1 认证流程
@router.post("/v1/auth/login")
async def v1_login():
    # 传统 JWT 认证
    # 查询 users 表
    # 生成自定义 JWT
    pass

# V2 认证流程  
@router.post("/v2/auth/signin")
async def v2_signin():
    # Supabase Auth
    # 使用 Supabase SDK
    # 返回 Supabase JWT
    pass
```

### 权限控制
```python
# V1 权限控制
@router.get("/v1/protected")
async def v1_protected(current_user: User = Depends(get_current_user_v1)):
    # 基于数据库的角色权限检查
    pass

# V2 权限控制
@router.get("/v2/protected")  
async def v2_protected(current_user: SupabaseUser = Depends(get_current_user)):
    # 基于用户元数据的权限检查
    # 自动 RLS 策略
    pass
```

## 📈 迁移监控

### 关键指标
1. **V2 采用率**: 新用户注册 V2 的比例
2. **迁移率**: V1 用户迁移到 V2 的比例  
3. **错误率**: V2 API 的错误率
4. **性能**: V2 vs V1 的响应时间对比
5. **用户满意度**: V2 用户的反馈评分

### 监控工具
```python
# 在每个 V2 端点添加监控
@router.post("/v2/auth/signin")
async def v2_signin():
    start_time = time.time()
    try:
        result = await SupabaseAuthService.sign_in(...)
        # 记录成功指标
        logger.info("v2_signin_success", duration=time.time()-start_time)
        return result
    except Exception as e:
        # 记录失败指标
        logger.error("v2_signin_error", error=str(e), duration=time.time()-start_time)
        raise
```

## 🎯 成功标准

### 短期目标（1-2个月）
- [ ] V2 API 稳定运行，错误率 < 1%
- [ ] 新用户 100% 使用 V2 注册
- [ ] V2 API 响应时间 < V1 API 响应时间
- [ ] 完成基础功能的 V2 实现

### 中期目标（3-6个月）
- [ ] 30% 的 V1 用户迁移到 V2
- [ ] V2 独有功能上线（实时通知、社交登录等）
- [ ] V2 API 覆盖所有核心业务功能
- [ ] 用户反馈积极，满意度 > 4.5/5

### 长期目标（6-12个月）
- [ ] 80% 用户使用 V2 API
- [ ] V1 API 进入维护模式
- [ ] 完全利用 Supabase 生态（Storage、Realtime 等）
- [ ] 开发效率提升 50%

## 🔄 回滚计划

如果 V2 出现重大问题：

### 立即回滚
```bash
# 1. 禁用 V2 路由
# 在 backend/app/api/__init__.py 中注释
# api_router.include_router(v2_router)

# 2. 重定向 V2 用户到 V1
# 在前端添加重定向逻辑

# 3. 数据同步
# 将 V2 用户数据同步回 V1 数据库
```

### 数据保护
- 定期备份 V2 数据
- 保持 V1 数据库的完整性
- 实现双向数据同步机制

## 💡 最佳实践

### 开发规范
1. **API 版本标识**: 所有 V2 端点必须以 `/v2/` 开头
2. **错误处理**: V2 使用统一的错误响应格式
3. **日志记录**: V2 操作必须记录详细日志
4. **测试覆盖**: V2 功能必须有完整的单元测试

### 用户体验
1. **向后兼容**: V2 API 响应格式尽量与 V1 兼容
2. **迁移工具**: 提供一键迁移功能
3. **文档完善**: 提供详细的 V2 API 文档
4. **支持渠道**: 建立 V2 专门的技术支持

这个策略确保了平滑、安全、可控的迁移过程，最大化了成功的可能性。
